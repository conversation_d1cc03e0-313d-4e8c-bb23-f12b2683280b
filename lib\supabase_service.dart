import 'package:supabase_flutter/supabase_flutter.dart';
import 'models/post.dart';
import 'models/comment.dart';
import 'dart:typed_data';
import 'models/app_notification.dart';
import 'models/chat.dart';
import 'models/message.dart';
import 'models/story.dart';
import 'models/group.dart';
import 'models/product.dart';
import 'models/reaction_type.dart';

class SupabaseService {
  final SupabaseClient _client = Supabase.instance.client;

  Future<AuthResponse> signUp({required String email, required String password, required String name}) async {
    final res = await _client.auth.signUp(
      email: email,
      password: password,
      data: {'name': name},
      emailRedirectTo: 'https://arzawo.com/welcome', // حدّد رابط إعادة التوجيه (غيّره حسب نطاقك)
    );

    try {
      // أنشئ صفاً فى جدول profiles ليُستعمل فوراً بدل انتظار التريجر
      await _client.from('profiles').insert({
        'id': res.user!.id,
        'name': name,
        'avatar_url': '',
      });
    } catch (_) {
      // قد يفشل إذا كان الصف موجوداً عبر تريجر، تجاهل
    }
    return res;
  }

  Future<AuthResponse> signIn({required String email, required String password}) {
    return _client.auth.signInWithPassword(email: email, password: password);
  }

  Future<List<Post>> fetchPosts() async {
    final uid = _client.auth.currentUser?.id;

    dynamic query = _client
        .from('posts')
        .select('''
          id,
          user_id,
          content,
          created_at,
          type,
          media_url,
          link_url,
          link_meta,
          bg_color,
          likes_count,
          dislikes_count,
          shares_count,
          comments_count,
          shared_post_id,
          original:posts!shared_post_id(id,user_id,content,created_at,type,media_url,link_url,link_meta,profiles(name,avatar_url)),
          reactions!left(type,user_id),
          profiles(name,avatar_url),
          saved:saved_posts!left(id,user_id)
        ''');

    // بدلاً من تقييد reactions بالمستخدم الحالى، نجلب جميع التفاعلات للحصول على ملخّص التفاعلات لكل منشور.

    query = query.order('created_at', ascending: false);

    final data = await query;

    return (data as List).map((row) {
      final profile = row['profiles'] ?? {};

      // ابنى ملخّص التفاعلات وعدد كل نوع، واستخرج تفاعل المستخدم الحالى إن وجد
      ReactionType userReaction = ReactionType.none;
      Map<ReactionType, int> reactionCounts = {};
      final reactions = row['reactions'] as List?; // قد تكون null أو []
      if (reactions != null && reactions.isNotEmpty) {
        for (final r in reactions) {
          final rt = _mapReaction(r['type'] as String?);
          reactionCounts[rt] = (reactionCounts[rt] ?? 0) + 1;

          if (r['user_id'] == uid) {
            userReaction = rt;
          }
        }
      }

      bool isSaved = false;
      final savedList = row['saved'] as List?;
      if (savedList != null && savedList.isNotEmpty) {
        isSaved = true;
      }

      Post? original;
      if (row['original'] != null) {
        // قد تُرجع Supabase كائناً مفرداً (Map) أو قائمة (List) اعتماداً على نوع الربط
        dynamic origData = row['original'];
        if (origData is List) {
          if (origData.isEmpty) origData = null;
          else origData = origData.first;
        }

        if (origData != null && origData is Map) {
          final orig = origData;
          final origProfile = orig['profiles'] ?? {};
          original = Post(
            id: orig['id'].toString(),
            userId: orig['user_id'].toString(),
            userName: origProfile['name'] ?? 'مستخدم',
            userAvatar: origProfile['avatar_url'] ?? '',
            content: orig['content'] ?? '',
            createdAt: DateTime.parse(orig['created_at']),
            type: _mapType(orig['type']),
            mediaUrl: orig['media_url'],
            linkUrl: orig['link_url'],
            linkMeta: orig['link_meta'],
            bgColor: orig['bg_color'],
            likesCount: 0,
            dislikesCount: 0,
            sharesCount: 0,
            commentsCount: 0,
            reactionCounts: const {},
          );
        }
      }

      return Post(
        id: row['id'].toString(),
        userId: row['user_id'].toString(),
        userName: profile['name'] ?? 'مستخدم',
        userAvatar: profile['avatar_url'] ?? '',
        content: row['content'] ?? '',
        createdAt: DateTime.parse(row['created_at']),
        type: _mapType(row['type']),
        mediaUrl: row['media_url'],
        linkUrl: row['link_url'],
        linkMeta: row['link_meta'],
        bgColor: row['bg_color'],
        likesCount: row['likes_count'] ?? 0,
        dislikesCount: row['dislikes_count'] ?? 0,
        sharesCount: row['shares_count'] ?? 0,
        commentsCount: row['comments_count'] ?? 0,
        reactionCounts: reactionCounts,
        originalPost: original,
        currentUserReaction: userReaction,
        isSaved: isSaved,
        viewsCount: row['views_count'] ?? 0,
      );
    }).toList();
  }

  PostType _mapType(String? value) {
    switch (value) {
      case 'image':
        return PostType.image;
      case 'video':
        return PostType.video;
      case 'link':
        return PostType.link;
      case 'shared':
        return PostType.shared;
      case 'audio':
        return PostType.audio;
      case 'voice':
        return PostType.voice;
      default:
        return PostType.text;
    }
  }

  Future<String> uploadMedia(Uint8List bytes, String path) async {
    // تحاول Supabase رفع الملف وتُرجع المسار عند النجاح أو ترمى استثناء عند الخطأ
    await _client.storage.from('media').uploadBinary(
      path,
      bytes,
      fileOptions: const FileOptions(cacheControl: '3600', upsert: false),
    );

    // إذا وصلنا إلى هنا فالرفع نجح. نحصل على رابط عام لعرض الملف.
    return _client.storage.from('media').getPublicUrl(path);
  }

  Future<void> createPost({required String content, PostType type = PostType.text, String? mediaUrl, String? linkUrl, Map<String, dynamic>? linkMeta, String? sharedPostId, String? bgColor}) async {
    await _client.from('posts').insert({
      'user_id': _client.auth.currentUser!.id,
      'content': content,
      'type': type.name,
      'media_url': mediaUrl,
      'link_url': linkUrl,
      'link_meta': linkMeta,
      'bg_color': bgColor,
      'shared_post_id': sharedPostId,
      'created_at': DateTime.now().toIso8601String(),
    });
  }

  Future<void> toggleReaction({required String postId, required ReactionType reaction}) async {
    final uid = _client.auth.currentUser!.id;

    // احصل على التفاعل الحالي (إن وجد)
    final existing = await _client
        .from('reactions')
        .select('id,type')
        .eq('post_id', postId)
        .eq('user_id', uid)
        .maybeSingle();

    ReactionType? oldReaction;
    if (existing != null) {
      oldReaction = _mapReaction(existing['type']);
    }

    if (oldReaction == reaction) {
      // إزالة التفاعل (عدم تفاعل)
      await _client.from('reactions').delete().eq('id', existing!['id']);
      await _updateCounters(postId, remove: reaction);
    } else {
      if (existing == null) {
        // إدراج تفاعل جديد
        await _client.from('reactions').insert({
          'post_id': postId,
          'user_id': uid,
          'type': reaction.value,
        });
      } else {
        // تحديث التفاعل
        await _client.from('reactions').update({'type': reaction.value}).eq('id', existing!['id']);
        await _updateCounters(postId, remove: oldReaction, add: reaction);
        return;
      }
      await _updateCounters(postId, add: reaction);
    }
  }

  Future<void> _updateCounters(String postId, {ReactionType? add, ReactionType? remove}) async {
    bool _isPositive(ReactionType t) => [
      ReactionType.like,
      ReactionType.celebrate,
      ReactionType.support,
      ReactionType.love,
      ReactionType.insightful,
      ReactionType.funny,
    ].contains(t);

    final updates = <String, int>{};
    if (add != null && _isPositive(add)) {
      updates['likes_count'] = 1;
    }
    if (remove != null && _isPositive(remove)) {
      updates['likes_count'] = (updates['likes_count'] ?? 0) - 1;
    }

    if (updates.isEmpty) return;
    await _client.rpc('increment_counters', params: {
      'post_id_param': postId,
      'likes_inc': updates['likes_count'] ?? 0,
      'dislikes_inc': 0,
    });
  }

  ReactionType _mapReaction(String? value) {
    return ReactionType.values.firstWhere(
      (rt) => rt.value == value,
      orElse: () => ReactionType.none,
    );
  }

  Future<Map<ReactionType, List<Map<String, dynamic>>>> getPostReactionDetails(String postId) async {
    try {
      // أولاً، جرب الاستعلام مع جدول profiles
      List<Map<String, dynamic>> response;
      try {
        response = await _client
            .from('reactions')
            .select('''
              type,
              user_id,
              profiles!inner(
                id,
                name,
                username,
                avatar_url
              )
            ''')
            .eq('post_id', postId)
            .order('created_at', ascending: false);
      } catch (profileError) {
        // إذا فشل، استخدم استعلام بسيط بدون profiles
        print('Profiles join failed, using simple query: $profileError');
        response = await _client
            .from('reactions')
            .select('type, user_id')
            .eq('post_id', postId)
            .order('created_at', ascending: false);
      }

      final Map<ReactionType, List<Map<String, dynamic>>> result = {};

      for (final item in response) {
        final reactionType = _mapReaction(item['type']);
        if (reactionType == ReactionType.none) continue;

        final userProfile = item['profiles'];
        if (userProfile != null) {
          // استخدام بيانات profiles إذا كانت متوفرة
          result.putIfAbsent(reactionType, () => []);
          result[reactionType]!.add({
            'id': userProfile['id'],
            'name': userProfile['name'] ?? 'مستخدم',
            'username': userProfile['username'],
            'avatar_url': userProfile['avatar_url'],
          });
        } else {
          // استخدام user_id فقط إذا لم تكن profiles متوفرة
          result.putIfAbsent(reactionType, () => []);
          result[reactionType]!.add({
            'id': item['user_id'],
            'name': 'مستخدم ${item['user_id'].substring(0, 8)}',
            'username': null,
            'avatar_url': null,
          });
        }
      }

      return result;
    } catch (e) {
      print('Error fetching reaction details: $e');
      return {};
    }
  }

  // ------------------ Stories ------------------ //

  StoryType _mapStoryType(String? v) {
    switch (v) {
      case 'image':
        return StoryType.image;
      case 'video':
        return StoryType.video;
      default:
        return StoryType.text;
    }
  }

  Future<void> createStory({required StoryType type, String? text, Uint8List? mediaBytes, String? mediaExt}) async {
    String? mediaUrl;
    if (mediaBytes != null && mediaExt != null) {
      final path = 'stories/${_client.auth.currentUser!.id}/${DateTime.now().millisecondsSinceEpoch}.$mediaExt';
      mediaUrl = await uploadMedia(mediaBytes, path);
    }

    await _client.from('stories').insert({
      'user_id': _client.auth.currentUser!.id,
      'type': type.name,
      'text': text,
      'media_url': mediaUrl,
      'created_at': DateTime.now().toIso8601String(),
      'expires_at': DateTime.now().add(const Duration(hours: 24)).toIso8601String(),
    });
  }

  List<Story> _rowsToStories(List rows) {
    return rows.map((r) {
      final profile = r['profiles'] ?? {};
      return Story(
        id: r['id'],
        userId: r['user_id'],
        userName: profile['name'] ?? 'مستخدم',
        userAvatar: profile['avatar_url'] ?? '',
        type: _mapStoryType(r['type']),
        text: r['text'],
        mediaUrl: r['media_url'],
        createdAt: DateTime.parse(r['created_at']),
        expiresAt: DateTime.parse(r['expires_at']),
      );
    }).toList();
  }

  Future<List<Story>> fetchStories() async {
    final nowIso = DateTime.now().toIso8601String();
    final rows = await _client
        .from('stories')
        .select('''id,user_id,type,text,media_url,created_at,expires_at, profiles(name,avatar_url)''')
        .gt('expires_at', nowIso)
        .order('created_at', ascending: false);

    return _rowsToStories(rows as List);
  }

  Stream<List<Story>> storiesStream() {
    return _client
        .from('stories')
        .stream(primaryKey: ['id'])
        .gt('expires_at', DateTime.now().toIso8601String())
        .order('created_at', ascending: false)
        .map((rows) => _rowsToStories(rows as List));
  }

  // ------------------ Comments ------------------ //

  CommentType _mapCommentType(String? v) {
    switch (v) {
      case 'image':
        return CommentType.image;
      case 'video':
        return CommentType.video;
      default:
        return CommentType.text;
    }
  }

  Future<void> createComment({
    required String postId,
    String? parentId,
    required String content,
    required CommentType type,
    String? mediaUrl,
  }) async {
    // إدراج التعليق
    await _client.from('comments').insert({
      'post_id': postId,
      'parent_id': parentId,
      'user_id': _client.auth.currentUser!.id,
      'content': content,
      'type': type.name,
      if (mediaUrl != null) 'media_url': mediaUrl,
      'created_at': DateTime.now().toIso8601String(),
    });

    // تحديث عدد التعليقات في المنشور
    await _incrementPostCommentsCount(postId);
  }

  /// تحديث عدد التعليقات في المنشور
  Future<void> _incrementPostCommentsCount(String postId) async {
    try {
      // استخدام RPC function لتحديث العدد بشكل آمن
      await _client.rpc('increment_post_comments', params: {
        'post_id_param': postId,
      });
    } catch (e) {
      // في حالة عدم وجود الدالة، نستخدم طريقة بديلة
      await _updatePostCommentsCountManually(postId);
    }
  }

  /// طريقة بديلة لتحديث عدد التعليقات يدوياً
  Future<void> _updatePostCommentsCountManually(String postId) async {
    try {
      // حساب عدد التعليقات الفعلي
      final count = await _client
          .from('comments')
          .select('id')
          .eq('post_id', postId)
          .count(CountOption.exact);

      // تحديث العدد في جدول المنشورات
      await _client
          .from('posts')
          .update({'comments_count': count.count})
          .eq('id', postId);
    } catch (e) {
      print('خطأ في تحديث عدد التعليقات: $e');
    }
  }

  Future<List<Comment>> fetchComments(String postId) async {
    final rows = await _client
        .from('comments')
        .select('''id,parent_id,content,type,media_url,created_at,user_id, profiles(name,avatar_url),likes_count''')
        .eq('post_id', postId)
        .order('created_at', ascending: false);

    return _toComments(rows as List, postId);
  }

  List<Comment> _toComments(List rows, String postId) {
    final Map<String, Comment> map = {};
    final List<Comment> roots = [];

    for (final r in rows) {
      final comment = Comment(
        id: r['id'],
        postId: postId,
        parentId: r['parent_id'],
        userId: r['user_id'],
        userName: (r['profiles']?['name']) ?? 'مستخدم',
        userAvatar: (r['profiles']?['avatar_url']) ?? '',
        content: r['content'] ?? '',
        createdAt: DateTime.parse(r['created_at']),
        type: _mapCommentType(r['type']),
        mediaUrl: r['media_url'],
        likesCount: r['likes_count'] ?? 0,
        currentUserReaction: ReactionType.none,
        replies: [],
      );
      map[comment.id] = comment;
    }

    for (final c in map.values) {
      if (c.parentId == null) {
        roots.add(c);
      } else if (map.containsKey(c.parentId)) {
        map[c.parentId]!.replies.add(c);
      }
    }
    return roots;
  }

  Stream<List<Comment>> commentsStream(String postId) {
    return _client
        .from('comments')
        .stream(primaryKey: ['id'])
        .eq('post_id', postId)
        .order('created_at', ascending: false)
        .map((rows) => _toComments(rows as List, postId));
  }

  /// جلب عدد التعليقات الفعلي للمنشور
  Future<int> getPostCommentsCount(String postId) async {
    try {
      final result = await _client
          .from('comments')
          .select('id')
          .eq('post_id', postId)
          .count(CountOption.exact);
      return result.count ?? 0;
    } catch (e) {
      return 0;
    }
  }

  // ------------------ Profile ------------------ //

  Future<Map<String, dynamic>?> fetchProfile(String userId) async {
    final data = await _client
        .from('profiles')
        .select('id,name,avatar_url,bio,followers_count,following_count,email')
        .eq('id', userId)
        .maybeSingle();
    return data;
  }

  Future<void> updateProfile({String? name, String? bio, String? avatarUrl}) async {
    await _client.from('profiles').update({
      if (name != null) 'name': name,
      if (bio != null) 'bio': bio,
      if (avatarUrl != null) 'avatar_url': avatarUrl,
    }).eq('id', _client.auth.currentUser!.id);
  }

  // ------------------ Social (Follow) ------------------ //

  /// جلب جميع المستخدمين (باستثناء المستخدم الحالى) مع حالة المتابعة
  Future<List<Map<String, dynamic>>> fetchUsers() async {
    final uid = _client.auth.currentUser!.id;

    // كل المستخدمين ماعدا الحالى
    final profiles = await _client
        .from('profiles')
        .select('id,name,avatar_url')
        .neq('id', uid);

    // قائمة من يتابعهم المستخدم الحالى
    final followingRows = await _client
        .from('follows')
        .select('following_id')
        .eq('follower_id', uid);

    final followingIds = (followingRows as List).map((e) => e['following_id'] as String).toSet();

    return (profiles as List).map((p) {
      final id = p['id'] as String;
      return {
        'id': id,
        'name': p['name'] ?? 'مستخدم',
        'avatar_url': p['avatar_url'] ?? '',
        'is_following': followingIds.contains(id),
      };
    }).toList();
  }

  /// تُرجع true إذا أصبح المستخدم الآخر مُتابَعاً بعد هذا النداء، وإلا false
  Future<bool> toggleFollow(String otherUserId) async {
    final uid = _client.auth.currentUser!.id;

    // تحقق مما إذا كان يتابع بالفعل
    final existing = await _client
        .from('follows')
        .select('id')
        .eq('follower_id', uid)
        .eq('following_id', otherUserId)
        .maybeSingle();

    bool nowFollowing;
    if (existing == null) {
      // إدراج متابعة جديدة
      await _client.from('follows').insert({'follower_id': uid, 'following_id': otherUserId});
      nowFollowing = true;
      // حدّث العدادات (يمكنك استبدالها بتريجر فى قاعدة البيانات)
      await _client.rpc('inc_follow_counters', params: {
        'target_id': otherUserId,
        'follower_id': uid,
        'delta': 1,
      }).catchError((_){});
    } else {
      // إلغاء المتابعة
      await _client.from('follows').delete().eq('id', existing['id']);
      nowFollowing = false;
      await _client.rpc('inc_follow_counters', params: {
        'target_id': otherUserId,
        'follower_id': uid,
        'delta': -1,
      }).catchError((_){});
    }
    return nowFollowing;
  }

  /// هل يتابع المستخدم الحالى المستخدم الآخر؟
  Future<bool> isFollowing(String otherUserId) async {
    final uid = _client.auth.currentUser!.id;
    final existing = await _client
        .from('follows')
        .select('id')
        .eq('follower_id', uid)
        .eq('following_id', otherUserId)
        .maybeSingle();
    return existing != null;
  }

  /// جلب منشورات مستخدم محدد
  Future<List<Post>> fetchPostsByUser(String userId) async {
    final uid = _client.auth.currentUser?.id;

    dynamic query = _client
        .from('posts')
        .select('''
          id,
          user_id,
          content,
          created_at,
          type,
          media_url,
          link_url,
          link_meta,
          bg_color,
          likes_count,
          dislikes_count,
          shares_count,
          comments_count,
          views_count,
          profiles(name,avatar_url),
          reactions(type,user_id),
          saved_posts(user_id)
        ''')
        .eq('user_id', userId)
        .order('created_at', ascending: false);

    final data = await query;

    return (data as List).map((row) {
      final profile = row['profiles'] ?? {};

      // بناء إحصائيات التفاعلات
      ReactionType userReaction = ReactionType.none;
      Map<ReactionType, int> reactionCounts = {};
      final reactions = row['reactions'] as List?;
      if (reactions != null && reactions.isNotEmpty) {
        for (final r in reactions) {
          final rt = _mapReaction(r['type'] as String?);
          reactionCounts[rt] = (reactionCounts[rt] ?? 0) + 1;

          if (r['user_id'] == uid) {
            userReaction = rt;
          }
        }
      }

      final savedPosts = row['saved_posts'] as List?;
      final isSaved = savedPosts != null && savedPosts.any((s) => s['user_id'] == uid);

      return Post(
        id: row['id'].toString(),
        userId: row['user_id'].toString(),
        userName: profile['name'] ?? 'مستخدم',
        userAvatar: profile['avatar_url'] ?? '',
        content: row['content'] ?? '',
        createdAt: DateTime.parse(row['created_at']),
        type: _mapType(row['type']),
        mediaUrl: row['media_url'],
        linkUrl: row['link_url'],
        linkMeta: row['link_meta'],
        bgColor: row['bg_color'],
        likesCount: row['likes_count'] ?? 0,
        dislikesCount: row['dislikes_count'] ?? 0,
        sharesCount: row['shares_count'] ?? 0,
        commentsCount: row['comments_count'] ?? 0,
        viewsCount: row['views_count'] ?? 0,
        reactionCounts: reactionCounts,
        currentUserReaction: userReaction,
        isSaved: isSaved,
      );
    }).toList();
  }

  // ------------------ Account ------------------ //

  Future<void> changeEmail(String newEmail, String currentPassword) async {
    // Re-authenticate then update email
    await _client.auth.signInWithPassword(email: _client.auth.currentUser!.email!, password: currentPassword);
    await _client.auth.updateUser(UserAttributes(email: newEmail));
  }

  Future<void> changePassword(String newPassword) async {
    await _client.auth.updateUser(UserAttributes(password: newPassword));
  }

  Future<void> signOut() async {
    await _client.auth.signOut();
  }

  Future<void> deactivateAccount() async {
    final uid = _client.auth.currentUser!.id;
    await _client.from('profiles').update({
      'status': 'deactivated',
      'deactivated_at': DateTime.now().toIso8601String(),
    }).eq('id', uid);
  }

  Future<void> deleteAccount() async {
    final uid = _client.auth.currentUser!.id;
    await _client.from('profiles').update({
      'status': 'pending_delete',
      'deleted_at': DateTime.now().add(const Duration(days: 30)).toIso8601String(),
    }).eq('id', uid);
  }

  // ------------------ User Settings ------------------ //

  Future<Map<String, dynamic>> fetchSettings() async {
    final data = await _client
        .from('user_settings')
        .select()
        .eq('user_id', _client.auth.currentUser!.id)
        .maybeSingle();
    return data ?? {};
  }

  Future<void> updateSettings(Map<String, dynamic> values) async {
    await _client.from('user_settings').upsert({
      'user_id': _client.auth.currentUser!.id,
      ...values,
    });
  }

  // ------------------ Notifications ------------------ //

  Future<List<AppNotification>> fetchNotifications() async {
    final uid = _client.auth.currentUser!.id;
    final rows = await _client
        .from('notifications')
        .select('''id,type,ref_id,created_at,read, sender:profiles!notifications_sender_id_fkey(name,avatar_url)''')
        .eq('receiver_id', uid)
        .order('created_at', ascending: false);

    return (rows as List).map((r) {
      return AppNotification(
        id: r['id'].toString(),
        senderId: '',
        senderName: (r['sender']?['name']) ?? 'مستخدم',
        senderAvatar: (r['sender']?['avatar_url']) ?? '',
        type: r['type'],
        refId: r['ref_id']?.toString(),
        createdAt: DateTime.parse(r['created_at']),
        read: r['read'] ?? false,
      );
    }).toList();
  }

  Future<int> unreadNotificationsCount() async {
    final uid = _client.auth.currentUser!.id;
    final res = await _client
        .from('notifications')
        .select('id')
        .eq('receiver_id', uid)
        .eq('read', false)
        .count(CountOption.exact);
    return res.count ?? 0;
  }

  Future<void> markNotificationRead(String id) async {
    await _client.from('notifications').update({'read': true}).eq('id', id);
  }

  Future<void> setNotificationRead(String id, bool read) async {
    await _client.from('notifications').update({'read': read}).eq('id', id);
  }

  Future<void> markAllNotificationsRead() async {
    final uid = _client.auth.currentUser!.id;
    await _client.from('notifications').update({'read': true}).eq('receiver_id', uid);
  }

  /// إرسال إشعار (live) إلى مستخدم معين عند بدء البث المباشر
  Future<void> createLiveNotification({required String receiverId, required String streamId}) async {
    await _client.from('notifications').insert({
      'receiver_id': receiverId,
      'sender_id': _client.auth.currentUser!.id,
      'type': 'live',
      'ref_id': streamId,
    });
  }

  // ------------------ Chats ------------------ //

  Future<String> getOrCreateChat(String otherId) async {
    final uid = _client.auth.currentUser!.id;
    // اجلب محادثة إن وجدت
    final existing = await _client
        .from('chats')
        .select('id')
        .or('and(user1.eq.$uid,user2.eq.$otherId),and(user1.eq.$otherId,user2.eq.$uid)')
        .maybeSingle();
    if (existing != null) return existing['id'].toString();

    // إنشاء جديدة
    final inserted = await _client.from('chats').insert({'user1': uid, 'user2': otherId}).select().single();
    return inserted['id'].toString();
  }

  Future<List<Chat>> fetchChats() async {
    final uid = _client.auth.currentUser!.id;
    final rows = await _client.from('chats').select('''id,user1,user2,last_message,last_message_at,
      profiles!chats_user1_fkey(id,name,avatar_url), profiles!chats_user2_fkey(id,name,avatar_url)''').order('last_message_at', ascending: false);

    return (rows as List).map((r) {
      final profilesList = (r['profiles'] as List?) ?? [];

      // ابحث عن ملف المستخدم الآخر (غير الحالى)
      Map<String, dynamic>? other;
      if (profilesList.isNotEmpty) {
        other = profilesList.firstWhere(
          (p) => p != null && p['id'] != uid,
          orElse: () => profilesList.first,
        );
      }

      // فى حال فشل الربط أو كان الجدول فارغاً، أنشئ قيمة افتراضية لتجنب الأعطال
      other ??= {'id': '', 'name': 'مستخدم', 'avatar_url': ''};

      return Chat(
        id: r['id'],
        otherId: other['id'] ?? '',
        otherName: (other['name'] ?? 'مستخدم') as String,
        otherAvatar: (other['avatar_url'] ?? '') as String,
        lastMessage: r['last_message'] ?? '',
        lastAt: r['last_message_at'] != null ? DateTime.parse(r['last_message_at']) : null,
      );
    }).toList();
  }

  Stream<List<Message>> messagesStream(String chatId) {
    return _client
        .from('messages')
        .stream(primaryKey: ['id'])
        .eq('chat_id', chatId)
        .order('created_at', ascending: true)
        .map((rows) => (rows as List).map(_mapMsg).toList());
  }

  Message _mapMsg(dynamic r) {
    return Message(
      id: r['id'].toString(),
      chatId: r['chat_id'],
      senderId: r['sender_id'],
      content: r['content'] ?? '',
      type: _mapMedia(r['media_type']),
      mediaUrl: r['media_url'],
      createdAt: DateTime.parse(r['created_at']),
    );
  }

  MediaType _mapMedia(String? v) {
    switch (v) {
      case 'image':
        return MediaType.image;
      case 'video':
        return MediaType.video;
      case 'link':
        return MediaType.link;
      case 'emoji':
        return MediaType.emoji;
      case 'audio':
        return MediaType.audio;
      case 'voice':
        return MediaType.voice;
      default:
        return MediaType.text;
    }
  }

  Future<void> sendMessage({required String chatId, required String content, MediaType type = MediaType.text, Uint8List? bytes}) async {
    String? url;
    if (bytes != null) {
      final ext = type == MediaType.image ? 'jpg' : (type == MediaType.video ? 'mp4' : 'aac');
      final path = 'chat/$chatId/${DateTime.now().millisecondsSinceEpoch}.$ext';
      url = await uploadMedia(bytes, path);
    }
    await _client.from('messages').insert({
      'chat_id': chatId,
      'sender_id': _client.auth.currentUser!.id,
      'content': content,
      'media_url': url,
      'media_type': type.name,
    });
  }

  Stream<List<Chat>> chatsStream() {
    final uid = _client.auth.currentUser!.id;
    return _client
        .from('chats')
        .stream(primaryKey: ['id'])
        .order('last_message_at', ascending: false)
        .map((rows) {
      return (rows as List).map((r) {
        final profilesList = (r['profiles'] as List?) ?? [];
        Map<String, dynamic>? other;
        if (profilesList.isNotEmpty) {
          other = profilesList.firstWhere(
            (p) => p != null && p['id'] != uid,
            orElse: () => profilesList.first,
          );
        }
        other ??= {'id': '', 'name': 'مستخدم', 'avatar_url': ''};

        return Chat(
          id: r['id'],
          otherId: other['id'] ?? '',
          otherName: (other['name'] ?? 'مستخدم') as String,
          otherAvatar: (other['avatar_url'] ?? '') as String,
          lastMessage: r['last_message'] ?? '',
          lastAt: r['last_message_at'] != null ? DateTime.parse(r['last_message_at']) : null,
          unreadCount: r['unread_count'] ?? 0,
        );
      }).toList();
    });
  }

  Future<void> markMessagesRead(String chatId) async {
    final uid = _client.auth.currentUser!.id;
    // يعتمد على وجود عمود read_by ARRAY<text>
    await _client.rpc('mark_messages_read', params: {
      'p_chat_id': chatId,
      'p_user_id': uid,
    });
  }

  // ------------------ Chat management ------------------ //

  Future<void> blockUser(String otherId) async {
    final uid = _client.auth.currentUser!.id;
    await _client.from('blocks').upsert({'user_id': uid, 'blocked_user_id': otherId});
  }

  Future<void> unblockUser(String otherId) async {
    await _client
        .from('blocks')
        .delete()
        .eq('user_id', _client.auth.currentUser!.id)
        .eq('blocked_user_id', otherId);
  }

  Future<void> archiveChat(String chatId, bool archived) async {
    await _client.from('chat_visibility').upsert({
      'chat_id': chatId,
      'user_id': _client.auth.currentUser!.id,
      'archived': archived,
    });
  }

  Future<void> deleteChatLocally(String chatId) async {
    await _client.from('chat_visibility').upsert({
      'chat_id': chatId,
      'user_id': _client.auth.currentUser!.id,
      'deleted': true,
    });
  }

  Future<void> reportChat(String chatId, String reason) async {
    await _client.from('chat_reports').insert({
      'chat_id': chatId,
      'reporter_id': _client.auth.currentUser!.id,
      'reason': reason,
    });
  }

  Future<void> editMessage({required String messageId, required String newContent}) async {
    await _client
        .from('messages')
        .update({'content': newContent, 'edited_at': DateTime.now().toIso8601String()})
        .eq('id', messageId);
  }

  Future<void> sendReply({required String chatId, required String replyTo, required String content}) async {
    await sendMessage(chatId: chatId, content: content, type: MediaType.text);
    // حدِّث حقل reply_to لأحدث رسالة أرسلتها بالـ content نفسه لمنع تضارب
    await _client
        .from('messages')
        .update({'reply_to': replyTo})
        .eq('chat_id', chatId)
        .eq('sender_id', _client.auth.currentUser!.id)
        .order('created_at', ascending: false)
        .limit(1);
  }

  // ------------------ Notifications realtime ------------------ //

  Stream<List<AppNotification>> notificationsStream() {
    final uid = _client.auth.currentUser!.id;
    return _client
        .from('notifications')
        .stream(primaryKey: ['id'])
        .eq('receiver_id', uid)
        .order('created_at', ascending: false)
        .map((rows) => (rows as List).map((r) {
              return AppNotification(
                id: r['id'].toString(),
                senderId: '',
                senderName: (r['sender']?['name']) ?? 'مستخدم',
                senderAvatar: (r['sender']?['avatar_url']) ?? '',
                type: r['type'],
                refId: r['ref_id']?.toString(),
                createdAt: DateTime.parse(r['created_at']),
                read: r['read'] ?? false,
              );
            }).toList());
  }

  // ------------------ Calls ------------------ //

  Future<String> createCall({required String chatId, required String type}) async {
    final row = await _client.from('calls').insert({
      'chat_id': chatId,
      'caller_id': _client.auth.currentUser!.id,
      'type': type,
      'status': 'ringing',
    }).select().single();
    return row['id'].toString();
  }

  Stream<String> callStatusStream(String callId) {
    return _client
        .from('calls')
        .stream(primaryKey: ['id'])
        .eq('id', callId)
        .limit(1)
        .map((rows) => rows.isNotEmpty ? rows.first['status'] as String : 'ended');
  }

  Future<void> updateCallStatus(String callId, String status) async {
    await _client.from('calls').update({'status': status}).eq('id', callId);
  }

  // ------------------ إدارة المنشورات (حفظ، حذف، خصوصية) ------------------ //

  Future<void> toggleSavePost(String postId) async {
    final uid = _client.auth.currentUser!.id;
    final existing = await _client
        .from('saved_posts')
        .select('id')
        .eq('post_id', postId)
        .eq('user_id', uid)
        .maybeSingle();

    if (existing == null) {
      await _client.from('saved_posts').insert({'post_id': postId, 'user_id': uid});
    } else {
      await _client.from('saved_posts').delete().eq('id', existing['id']);
    }
  }

  Future<void> deletePost(String postId) async {
    await _client.from('posts').delete().eq('id', postId);
  }

  Future<void> updatePostPrivacy(String postId, String visibility) async {
    await _client.from('posts').update({'visibility': visibility}).eq('id', postId);
  }

  Future<void> updatePostContent(String postId, String newContent) async {
    await _client.from('posts').update({'content': newContent}).eq('id', postId);
  }

  Future<void> updatePostCommentPermission(String postId, String permission) async {
    await _client.from('posts').update({'comment_permission': permission}).eq('id', postId);
  }

  // ------------------ Stories management ------------------ //

  Future<void> deleteStory(String storyId) async {
    await _client.from('stories').delete().eq('id', storyId);
  }

  Future<void> reportStory(String storyId, String reason) async {
    await _client.from('story_reports').insert({
      'story_id': storyId,
      'reporter_id': _client.auth.currentUser!.id,
      'reason': reason,
    });
  }

  // ------------------ Story interactions ------------------ //

  Future<void> toggleStoryReaction({required String storyId, required ReactionType type}) async {
    final uid = _client.auth.currentUser!.id;
    final existing = await _client
        .from('story_reactions')
        .select('id,type')
        .eq('story_id', storyId)
        .eq('user_id', uid)
        .maybeSingle();

    if (existing == null) {
      await _client.from('story_reactions').insert({
        'story_id': storyId,
        'user_id': uid,
        'type': type.name,
      });
    } else {
      if (existing['type'] == type.name) {
        // remove reaction
        await _client.from('story_reactions').delete().eq('id', existing['id']);
      } else {
        await _client.from('story_reactions').update({'type': type.name}).eq('id', existing['id']);
      }
    }
  }

  Future<void> createStoryComment({required String storyId, required String content}) async {
    await _client.from('story_comments').insert({
      'story_id': storyId,
      'user_id': _client.auth.currentUser!.id,
      'content': content,
    });
  }

  Stream<List<Comment>> storyCommentsStream(String storyId) {
    return _client
        .from('story_comments')
        .stream(primaryKey: ['id'])
        .eq('story_id', storyId)
        .order('created_at', ascending: false)
        .map((rows) => (rows as List).map((r) {
              return Comment(
                id: r['id'].toString(),
                postId: storyId,
                userId: r['user_id'],
                content: r['content'] ?? '',
                type: CommentType.text,
                createdAt: DateTime.parse(r['created_at']),
                userName: r['profiles']?['name'] ?? 'مستخدم',
                userAvatar: r['profiles']?['avatar_url'] ?? '',
                likesCount: 0,
              );
            }).toList());
  }

  Future<List<Map<String, dynamic>>> fetchFollowingUsers() async {
    final uid = _client.auth.currentUser!.id;
    final rows = await _client.rpc('followers_list', params: {'uid': uid});
    // إذا لم تتوفر الدالة؛ fallback إلى استعلام مباشر
    if (rows is List && rows.isNotEmpty && rows.first is Map) {
      return List<Map<String, dynamic>>.from(rows);
    }
    final data = await _client.from('follows').select('followee:profiles(id,name,avatar_url)').eq('follower_id', uid);
    return (data as List).map((e) => e['followee'] as Map<String, dynamic>).toList();
  }

  // ------------------ Groups ------------------ //

  GroupPrivacy _mapPrivacy(String? v) {
    switch (v) {
      case 'private':
        return GroupPrivacy.private;
      case 'hidden':
        return GroupPrivacy.hidden;
      default:
        return GroupPrivacy.public;
    }
  }

  Future<List<Group>> fetchGroups() async {
    final uid = _client.auth.currentUser?.id;
    final data = await _client
        .from('groups')
        .select('''id, name, description, owner_id, created_at, members:group_members(count), membership:group_members!left(user_id)''');

    return (data as List).map((row) {
      final membersList = row['members'] as List?;
      int count = 0;
      if (membersList != null && membersList.isNotEmpty) {
        final first = membersList.first;
        if (first is Map && first.containsKey('count')) {
          count = first['count'] as int;
        }
      }
      bool joined = false;
      final membership = row['membership'] as List?;
      if (uid != null && membership != null && membership.any((m) => m['user_id'] == uid)) {
        joined = true;
      }
      return Group(
        id: row['id'].toString(),
        name: row['name'] ?? '',
        description: row['description'] ?? '',
        ownerId: row['owner_id'] ?? '',
        coverUrl: row['cover_url'] ?? '',
        privacy: _mapPrivacy(row['privacy']),
        createdAt: DateTime.parse(row['created_at']),
        membersCount: count,
        joined: joined,
        postsCount: 0,
        isAdmin: false,
      );
    }).toList();
  }

  Future<void> createGroup({required String name, String description = ''}) async {
    await _client.from('groups').insert({
      'name': name,
      'description': description,
      'owner_id': _client.auth.currentUser!.id,
      'created_at': DateTime.now().toIso8601String(),
    });
  }

  Future<void> toggleGroupMembership(String groupId) async {
    final uid = _client.auth.currentUser!.id;
    final existing = await _client
        .from('group_members')
        .select('id')
        .eq('group_id', groupId)
        .eq('user_id', uid)
        .maybeSingle();
    if (existing != null) {
      await _client.from('group_members').delete().eq('id', existing['id']);
    } else {
      await _client.from('group_members').insert({'group_id': groupId, 'user_id': uid});
    }
  }

  Future<Group> fetchGroup(String groupId) async {
    final uid = _client.auth.currentUser?.id;

    var query = _client
        .from('groups')
        .select('''
          id,name,description,cover_url,owner_id,privacy,created_at,
          posts:group_posts(count),
          members:group_members(count),
          role:group_members!left(user_id,role)
        ''')
        .eq('id', groupId);

    final data = await query.maybeSingle();

    if (data == null) throw 'Group not found';
    final count = ((data['members'] as List?)?.first?['count'] ?? 0) as int;
    final postsCount = ((data['posts'] as List?)?.first?['count'] ?? 0) as int;
    final joined = (data['role'] as List?)?.isNotEmpty ?? false;
    final isAdmin = (data['role'] as List?)?.any((r) => r['role'] == 'admin') ?? false;
    return Group(
      id: data['id'].toString(),
      name: data['name'] ?? '',
      description: data['description'] ?? '',
      ownerId: data['owner_id'] ?? '',
      coverUrl: data['cover_url'] ?? '',
      privacy: _mapPrivacy(data['privacy']),
      createdAt: DateTime.parse(data['created_at']),
      membersCount: count,
      postsCount: postsCount,
      joined: joined,
      isAdmin: isAdmin,
    );
  }

  Future<void> requestJoin(String groupId) async {
    await _client.from('group_join_requests').insert({'group_id': groupId, 'user_id': _client.auth.currentUser!.id});
  }

  Future<void> toggleMembership(String groupId) async {
    final uid = _client.auth.currentUser!.id;
    final existing = await _client
        .from('group_members')
        .select('id')
        .eq('group_id', groupId)
        .eq('user_id', uid)
        .maybeSingle();
    if (existing != null) {
      await _client.from('group_members').delete().eq('id', existing['id']);
    } else {
      await _client.from('group_members').insert({'group_id': groupId, 'user_id': uid});
    }
  }

  Future<List<Post>> fetchGroupPosts(String groupId, {PostType? typeFilter}) async {
    // ابدأ الاستعلام بالفلترة، ثم أضف الترتيب حتى لا يتغيّر نوع الـ builder ويمنع EQ لاحقًا
    dynamic query = _client
        .from('group_posts')
        .select('''id,user_id,content,created_at,type,media_url,link_url,link_meta,bg_color,likes_count,dislikes_count,comments_count,shares_count,views_count,pinned, profiles(name,avatar_url)''')
        .eq('group_id', groupId);

    if (typeFilter != null) {
      query = query.eq('type', typeFilter.name);
    }

    query = query.order('pinned', ascending: false).order('created_at', ascending: false);

    final rows = await query;

    return (rows as List).map((row) {
      final profile = row['profiles'] ?? {};
      return Post(
        id: row['id'].toString(),
        userId: row['user_id'].toString(),
        userName: profile['name'] ?? 'مستخدم',
        userAvatar: profile['avatar_url'] ?? '',
        content: row['content'] ?? '',
        createdAt: DateTime.parse(row['created_at']),
        type: _mapType(row['type']),
        mediaUrl: row['media_url'],
        linkUrl: row['link_url'],
        linkMeta: row['link_meta'],
        bgColor: row['bg_color'],
        likesCount: row['likes_count'] ?? 0,
        dislikesCount: row['dislikes_count'] ?? 0,
        sharesCount: row['shares_count'] ?? 0,
        commentsCount: row['comments_count'] ?? 0,
        viewsCount: row['views_count'] ?? 0,
      );
    }).toList();
  }

  Future<void> createGroupPost({required String groupId, required String content, PostType type = PostType.text, String? mediaUrl, String? linkUrl, Map<String, dynamic>? linkMeta}) async {
    await _client.from('group_posts').insert({
      'group_id': groupId,
      'user_id': _client.auth.currentUser!.id,
      'content': content,
      'type': type.name,
      'media_url': mediaUrl,
      'link_url': linkUrl,
      'link_meta': linkMeta,
      'created_at': DateTime.now().toIso8601String(),
    });
  }

  Future<List<Map<String, dynamic>>> fetchGroupMembers(String groupId) async {
    final uid = _client.auth.currentUser?.id;
    final rows = await _client
        .from('group_members')
        .select('role, user:profiles(id,name,avatar_url)')
        .eq('group_id', groupId);
    return (rows as List).map((r) {
      final u = r['user'] as Map;
      return {
        'id': u['id'],
        'name': u['name'],
        'avatar': u['avatar_url'],
        'role': r['role'],
        'isMe': u['id'] == uid,
      };
    }).toList();
  }

  Future<void> promoteAdmin(String groupId, String memberId, bool makeAdmin) async {
    await _client
        .from('group_members')
        .update({'role': makeAdmin ? 'admin' : 'member'})
        .eq('group_id', groupId)
        .eq('user_id', memberId);
  }

  Future<void> kickMember(String groupId, String memberId) async {
    await _client
        .from('group_members')
        .delete()
        .eq('group_id', groupId)
        .eq('user_id', memberId);
  }

  Future<void> updateGroupPrivacy(String groupId, GroupPrivacy p) async {
    await _client.from('groups').update({'privacy': p.name}).eq('id', groupId);
  }

  Future<List<Map<String, dynamic>>> fetchJoinRequests(String groupId) async {
    final rows = await _client
        .from('group_join_requests')
        .select('id, user:profiles(id,name,avatar_url)')
        .eq('group_id', groupId);
    return (rows as List).map((r) => {
          'id': r['id'],
          'userId': r['user']['id'],
          'name': r['user']['name'],
          'avatar': r['user']['avatar_url'],
        }).toList();
  }

  Future<void> respondJoinRequest(String requestId, bool approve) async {
    final row = await _client.from('group_join_requests').select().eq('id', requestId).maybeSingle();
    if (row == null) return;
    if (approve) {
      await _client.from('group_members').insert({'group_id': row['group_id'], 'user_id': row['user_id']});
    }
    await _client.from('group_join_requests').delete().eq('id', requestId);
  }

  // ------------------ Views ------------------ //

  Future<void> incrementPostViews(String postId) async {
    await _client.rpc('increment_post_views', params: {'post_id_param': postId}).catchError((_){}) ;
  }

  // ------------------ Products ------------------ //

  String _conditionToString(ProductCondition c) => c == ProductCondition.used ? 'used' : 'new';
  String _sellerTypeToString(SellerType s) => s == SellerType.merchant ? 'merchant' : 'individual';
  String _deliveryMethodToString(DeliveryMethod d) => d == DeliveryMethod.delivery ? 'delivery' : 'pickup';
  String _contactMethodToString(ContactMethod m) => m == ContactMethod.phone ? 'phone' : 'in_app_chat';
  String _paymentChannelToString(PaymentChannel p) {
    switch (p) {
      case PaymentChannel.cashOnDelivery:
        return 'cash_on_delivery';
      case PaymentChannel.bankTransfer:
        return 'bank_transfer';
      default:
        return 'other';
    }
  }

  ProductCondition _mapProductCondition(String? v) => v == 'used' ? ProductCondition.used : ProductCondition.newItem;
  SellerType _mapSellerType(String? v) => v == 'merchant' ? SellerType.merchant : SellerType.individual;
  DeliveryMethod _mapDeliveryMethod(String? v) => v == 'pickup' ? DeliveryMethod.pickup : DeliveryMethod.delivery;
  ContactMethod _mapContactMethod(String? v) => v == 'phone' ? ContactMethod.phone : ContactMethod.inAppChat;
  PaymentChannel _mapPaymentChannel(String v) {
    switch (v) {
      case 'bank_transfer':
        return PaymentChannel.bankTransfer;
      case 'other':
        return PaymentChannel.other;
      default:
        return PaymentChannel.cashOnDelivery;
    }
  }

  Future<String> createProduct({
    required String name,
    required String description,
    required double price,
    required bool negotiable,
    required String category,
    String? brand,
    required String country,
    required String currency,
    required String city,
    String? address,
    required ContactMethod contactMethod,
    String? phone,
    required ProductCondition condition,
    int quantity = 1,
    required SellerType sellerType,
    required DeliveryMethod deliveryMethod,
    double? deliveryCost,
    required List<PaymentChannel> paymentMethods,
    required List<Uint8List> imagesBytes,
    required List<String> imagesExt,
  }) async {
    assert(imagesBytes.isNotEmpty, 'At least one image is required');
    assert(imagesBytes.length == imagesExt.length);

    // Step 1: insert product row (without images yet)
    final insertRes = await _client
        .from('products')
        .insert({
          'user_id': _client.auth.currentUser!.id,
          'name': name,
          'description': description,
          'price': price,
          'negotiable': negotiable,
          'category': category,
          'brand': brand,
          'country': country,
          'currency': currency,
          'city': city,
          'address': address,
          'contact_method': _contactMethodToString(contactMethod),
          'phone': phone,
          'condition': _conditionToString(condition),
          'quantity': quantity,
          'seller_type': _sellerTypeToString(sellerType),
          'delivery_method': _deliveryMethodToString(deliveryMethod),
          'delivery_cost': deliveryCost,
          'payment_methods': paymentMethods.map(_paymentChannelToString).toList(),
          'created_at': DateTime.now().toIso8601String(),
        })
        .select('id')
        .single();

    final productId = insertRes['id'] as String;

    // Step 2: upload images and insert product_images rows
    for (int i = 0; i < imagesBytes.length; i++) {
      final bytes = imagesBytes[i];
      final ext = imagesExt[i];
      final path = 'product_images/$productId/$i.$ext';
      final url = await uploadMedia(bytes, path);
      await _client.from('product_images').insert({
        'product_id': productId,
        'url': url,
        'idx': i,
      });
    }

    return productId;
  }

  Product _rowToProduct(Map row, {String? currentUserId}) {
    final List<ProductImage> imgs = [];
    final imagesRows = row['images'] as List?;
    if (imagesRows != null) {
      for (final im in imagesRows) {
        imgs.add(ProductImage(url: im['url'], index: im['idx']));
      }
      imgs.sort((a, b) => a.index.compareTo(b.index));
    }

    final profile = row['profiles'] ?? {};
    final likesList = row['likes'] as List? ?? [];
    bool likedByMe = currentUserId != null && likesList.any((l) => l['user_id'] == currentUserId);

    return Product(
      id: row['id'],
      userId: row['user_id'],
      userName: profile['name'] ?? 'مستخدم',
      userAvatar: profile['avatar_url'] ?? '',
      images: imgs,
      name: row['name'],
      description: row['description'],
      price: (row['price'] as num).toDouble(),
      negotiable: row['negotiable'] ?? false,
      category: row['category'],
      brand: row['brand'],
      country: row['country'] ?? '',
      currency: row['currency'] ?? 'USD',
      city: row['city'],
      address: row['address'],
      contactMethod: _mapContactMethod(row['contact_method']),
      phone: row['phone'],
      condition: _mapProductCondition(row['condition']),
      quantity: row['quantity'] ?? 1,
      sellerType: _mapSellerType(row['seller_type']),
      deliveryMethod: _mapDeliveryMethod(row['delivery_method']),
      deliveryCost: (row['delivery_cost'] as num?)?.toDouble(),
      paymentMethods: (row['payment_methods'] as List?)?.map((e) => _mapPaymentChannel(e as String)).toList() ?? [PaymentChannel.cashOnDelivery],
      createdAt: DateTime.parse(row['created_at']),
      viewsCount: row['views_count'] ?? 0,
      likesCount: row['likes_count'] ?? 0,
      commentsCount: row['comments_count'] ?? 0,
      sharesCount: row['shares_count'] ?? 0,
      likedByMe: likedByMe,
    );
  }

  Stream<List<Product>> productsStream() {
    final uid = _client.auth.currentUser?.id;
    return _client
        .from('products')
        .stream(primaryKey: ['id'])
        .order('created_at', ascending: false)
        .map((rows) => (rows as List)
            .map((r) => _rowToProduct(r as Map, currentUserId: uid))
            .toList());
  }

  Future<void> toggleProductLike(String productId) async {
    final uid = _client.auth.currentUser!.id;
    final existing = await _client
        .from('product_likes')
        .select()
        .eq('product_id', productId)
        .eq('user_id', uid)
        .maybeSingle();

    if (existing == null) {
      await _client.from('product_likes').insert({'product_id': productId, 'user_id': uid});
    } else {
      await _client.from('product_likes').delete().eq('product_id', productId).eq('user_id', uid);
    }
  }

  Future<void> incrementProductViews(String productId) async {
    await _client.from('product_views').upsert({
      'product_id': productId,
      'user_id': _client.auth.currentUser?.id,
    });
  }

  Future<void> incrementProductShares(String productId) async {
    await _client.from('product_shares').insert({'product_id': productId, 'user_id': _client.auth.currentUser?.id}).catchError((_){ });
  }

  Future<void> deleteProduct(String productId) async {
    await _client.from('products').delete().eq('id', productId);
  }

  // ------------------ Product Comments ------------------ //

  Future<void> createProductComment({
    required String productId,
    String? parentId,
    required String content,
    required CommentType type,
    String? mediaUrl,
  }) async {
    // إدراج التعليق
    await _client.from('product_comments').insert({
      'product_id': productId,
      'parent_id': parentId,
      'user_id': _client.auth.currentUser!.id,
      'content': content,
      'type': type.name,
      if (mediaUrl != null) 'media_url': mediaUrl,
      'created_at': DateTime.now().toIso8601String(),
    });

    // تحديث عدد التعليقات في المنتج
    await _incrementProductCommentsCount(productId);
  }

  /// تحديث عدد التعليقات في المنتج
  Future<void> _incrementProductCommentsCount(String productId) async {
    try {
      // حساب عدد التعليقات الفعلي
      final count = await _client
          .from('product_comments')
          .select('id')
          .eq('product_id', productId)
          .count(CountOption.exact);

      // تحديث العدد في جدول المنتجات
      await _client
          .from('products')
          .update({'comments_count': count.count})
          .eq('id', productId);
    } catch (e) {
      print('خطأ في تحديث عدد تعليقات المنتج: $e');
    }
  }

  Stream<List<Comment>> productCommentsStream(String productId) {
    return _client
        .from('product_comments')
        .stream(primaryKey: ['id'])
        .eq('product_id', productId)
        .order('created_at', ascending: false)
        .map((rows) => _toComments(rows as List, productId));
  }

  // ------------------ Helper Queries ------------------ //

  Future<List<ProductImage>> fetchProductImages(String productId) async {
    final rows = await _client
        .from('product_images')
        .select('url,idx')
        .eq('product_id', productId)
        .order('idx')
        .limit(6);

    return rows
        .map<ProductImage>((r) => ProductImage(url: r['url'] as String, index: r['idx'] as int))
        .toList();
  }

  // ------------------ Saved Posts ------------------ //
  Future<List<Post>> fetchSavedPosts() async {
    final all = await fetchPosts();
    return all.where((p) => p.isSaved).toList();
  }
} 