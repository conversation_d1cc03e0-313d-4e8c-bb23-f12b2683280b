import 'package:flutter/material.dart';
import '../models/post.dart';
import '../models/reaction_type.dart';
import '../supabase_service.dart';
import 'package:video_player/video_player.dart';
import 'package:chewie/chewie.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../widgets/comments_sheet.dart';
import '../widgets/share_post_sheet.dart';
import 'dart:async';

class VideosPage extends StatefulWidget {
  const VideosPage({super.key});

  @override
  State<VideosPage> createState() => _VideosPageState();
}

class _VideosPageState extends State<VideosPage> {
  final PageController _pageController = PageController();
  int _current = 0;

  List<Post> _videos = [];
  bool _loading = true;
  RealtimeChannel? _channel;

  @override
  void initState() {
    super.initState();
    _loadVideos();
    _subscribeRealtime();
  }

  void _loadVideos() async {
    final list = await SupabaseService().fetchPosts();
    final vids = list.where((p) => p.type == PostType.video && p.mediaUrl != null).toList();
    if (mounted) {
      setState(() {
        _videos = vids;
        _loading = false;
      });
    }
  }

  void _subscribeRealtime() {
    final client = Supabase.instance.client;
    _channel = client.channel('public:posts');

    _channel!.onPostgresChanges(
      event: PostgresChangeEvent.insert,
      schema: 'public',
      table: 'posts',
      callback: (payload, [ref]) {
        final newRow = payload.newRecord as Map<String, dynamic>?;
        if (newRow != null && newRow['type'] == 'video') {
          _loadVideos();
        }
      },
    );

    _channel!.subscribe();
  }

  @override
  void dispose() {
    _pageController.dispose();
    if (_channel != null) {
      Supabase.instance.client.removeChannel(_channel!);
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (_loading) {
      return const Center(child: CircularProgressIndicator());
    }
    if (_videos.isEmpty) {
      return const Center(child: Text('لا توجد فيديوهات بعد'));
    }
    return PageView.builder(
      controller: _pageController,
      scrollDirection: Axis.vertical,
      itemCount: _videos.length,
      onPageChanged: (i) => setState(() => _current = i),
      itemBuilder: (context, index) {
        final post = _videos[index];
        return _VideoItem(
          post: post,
          isActive: index == _current,
          onFinished: () {
            if (index < _videos.length - 1) {
              _pageController.animateToPage(index + 1, duration: const Duration(milliseconds: 300), curve: Curves.easeOut);
            }
          },
        );
      },
    );
  }
}

class _VideoItem extends StatefulWidget {
  final Post post;
  final bool isActive;
  final VoidCallback onFinished;
  const _VideoItem({required this.post, required this.isActive, required this.onFinished});

  @override
  State<_VideoItem> createState() => _VideoItemState();
}

class _VideoItemState extends State<_VideoItem> with AutomaticKeepAliveClientMixin {
  late VideoPlayerController _videoController;
  ChewieController? _chewie;
  bool _isFollowing = false;
  bool _loadingFollow = true;
  bool _triggeredNext = false;
  bool _showControls = true; // متغير لإظهار/إخفاء الأزرار
  Timer? _hideControlsTimer; // مؤقت لإخفاء الأزرار تلقائياً


  // Local mutable copy to reflect live counts
  late Post _post;

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    _post = widget.post;
    _videoController = VideoPlayerController.network(widget.post.mediaUrl!);
    _videoController.initialize().then((_) => setState(() {}));
    _chewie = ChewieController(
      videoPlayerController: _videoController,
      autoPlay: widget.isActive, // تشغيل تلقائي عند النشاط
      looping: true, // تكرار الفيديو مثل Facebook
      showControls: false, // إخفاء الكونترولز لنضع أزرارنا المخصصة
      allowMuting: true,
      allowPlaybackSpeedChanging: false, // تبسيط الواجهة
      optionsTranslation: OptionsTranslation(
        playbackSpeedButtonText: 'سرعة التشغيل',
        cancelButtonText: 'إلغاء',
      ),
    );

    // إظهار الأزرار في البداية وبدء مؤقت الإخفاء
    _startHideTimer();

    SupabaseService().isFollowing(widget.post.userId).then((res) {
      if (mounted) setState(() {
        _isFollowing = res;
        _loadingFollow = false;
      });
    });

    _videoController.addListener(() {
      if (widget.isActive) {
        // Reset flag when video rewinds / becomes active again
        if (!_videoController.value.isPlaying && _videoController.value.position == Duration.zero) {
          _triggeredNext = false;
        }

        final position = _videoController.value.position;
        final duration = _videoController.value.duration;
        if (duration != null && duration.inMilliseconds > 0) {
          final remaining = duration - position;

          // إذا عاد المستخدم بالزمن أو ما زال أمامه أكثر من 0.3 ثانية (الفيديو لم ينته بعد)
          if (remaining > const Duration(milliseconds: 300)) {
            _triggeredNext = false; // السماح بالتمرير مرة أخرى عند الاقتراب من نهاية الفيديو
          }

          // مرّر تلقائياً فقط عند الانتهاء (باقى ≤ 0.3 ثانية)
          if (!_triggeredNext && remaining <= const Duration(milliseconds: 300)) {
            _triggeredNext = true;
            widget.onFinished();
          }
        }
      } else {
        _triggeredNext = false; // reset when item becomes inactive
      }
    });
  }

  @override
  void didUpdateWidget(covariant _VideoItem oldWidget) {
    super.didUpdateWidget(oldWidget);
    // When item becomes active again (user رجع للخلف)
    if (widget.isActive && !oldWidget.isActive) {
      // أعد الفيديو للبداية وأعد ضبط العلَم
      _videoController.seekTo(Duration.zero);
      _triggeredNext = false;
    }

    if (widget.isActive && !_videoController.value.isPlaying) {
      _videoController.play();
    } else if (!widget.isActive && _videoController.value.isPlaying) {
      _videoController.pause();
    }
  }

  @override
  void dispose() {
    _hideControlsTimer?.cancel();
    _chewie?.dispose();
    _videoController.dispose();
    super.dispose();
  }

  // دالة لإظهار الأزرار وإخفائها تلقائياً
  void _toggleControls() {
    setState(() {
      _showControls = !_showControls;
    });

    if (_showControls) {
      _startHideTimer();
    }
  }

  // بدء مؤقت إخفاء الأزرار
  void _startHideTimer() {
    _hideControlsTimer?.cancel();
    _hideControlsTimer = Timer(const Duration(seconds: 3), () {
      if (mounted) {
        setState(() {
          _showControls = false;
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    if (!_videoController.value.isInitialized) {
      return const Center(child: CircularProgressIndicator());
    }

    // حساب نسبة العرض للفيديو
    final videoAspectRatio = _videoController.value.aspectRatio;
    final screenSize = MediaQuery.of(context).size;
    final screenAspectRatio = screenSize.width / screenSize.height;

    return Stack(
      children: [
        // عرض الفيديو بملء الشاشة مثل Facebook
        Positioned.fill(
          child: GestureDetector(
            onTap: _toggleControls, // النقر لإظهار/إخفاء الأزرار
            child: Container(
              color: Colors.black,
              child: Center(
                child: AspectRatio(
                  aspectRatio: videoAspectRatio,
                  child: SizedBox(
                    width: screenSize.width,
                    height: videoAspectRatio > screenAspectRatio
                        ? screenSize.width / videoAspectRatio  // فيديو عمودي
                        : screenSize.height,  // فيديو أفقي
                    child: Chewie(controller: _chewie!),
                  ),
                ),
              ),
            ),
          ),
        ),

        // أزرار التفاعل في الجانب الأيمن مثل TikTok (وسط الشاشة) - تظهر دائماً
        Positioned(
          right: 16,
          top: MediaQuery.of(context).size.height * 0.4,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // زر اللايك مع إمكانية إظهار كل التفاعلات عند النقر المستمر
              _buildLikeButton(),
              const SizedBox(height: 20),
              // زر التعليقات
              _buildActionButton(
                icon: Icons.comment,
                count: _post.commentsCount,
                onTap: _openComments,
              ),
              const SizedBox(height: 20),
              // زر المشاركة
              _buildActionButton(
                icon: Icons.share,
                count: _post.sharesCount,
                onTap: _openShare,
              ),
              const SizedBox(height: 20),
              // زر المشاهدات
              _buildActionButton(
                icon: Icons.visibility,
                count: _post.viewsCount,
                onTap: null,
              ),
            ],
          ),
        ),

        // معلومات المستخدم وزر المتابعة
        if (_showControls)
          Positioned(
            top: 40,
            right: 16,
            child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircleAvatar(backgroundImage: NetworkImage(widget.post.userAvatar), radius: 18),
              const SizedBox(width: 8),
              Text(widget.post.userName, style: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold)),
              const SizedBox(width: 8),
              _loadingFollow
                  ? const SizedBox(width: 20, height: 20, child: CircularProgressIndicator(color: Colors.white, strokeWidth: 2))
                  : TextButton(
                      onPressed: () async {
                        setState(() => _loadingFollow = true);
                        final nowFollow = await SupabaseService().toggleFollow(widget.post.userId);
                        if (mounted) setState(() {
                          _isFollowing = nowFollow;
                          _loadingFollow = false;
                        });
                      },
                      style: TextButton.styleFrom(
                        backgroundColor: _isFollowing ? Colors.grey.withOpacity(0.6) : Colors.red,
                        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
                      ),
                      child: Text(
                        _isFollowing ? 'متابَع' : 'متابعة',
                        style: const TextStyle(color: Colors.white),
                      ),
                    ),
            ],
          ),
        ),

        // وصف الفيديو أسفل
        if (widget.post.content.isNotEmpty)
          Positioned(
            bottom: 70,
            right: 16,
            child: SizedBox(
              width: MediaQuery.of(context).size.width * 0.7,
              child: Text(widget.post.content, style: const TextStyle(color: Colors.white), maxLines: 3, overflow: TextOverflow.ellipsis),
            ),
          ),

        // أزرار الإعجاب والتعليق والمشاركة – على الجانب الأيمن مثل تيك توك
        Positioned(
          right: 8,
          bottom: 120,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              _actionButton(
                icon: Icons.favorite,
                active: _post.currentUserReaction == ReactionType.like,
                activeColor: Colors.red,
                count: _post.likesCount,
                onTap: () => _react(ReactionType.like),
              ),
              const SizedBox(height: 16),
              _actionButton(
                icon: Icons.comment,
                active: false,
                count: _post.commentsCount,
                onTap: _openComments,
              ),
              const SizedBox(height: 16),
              _actionButton(
                icon: Icons.share,
                active: false,
                count: _post.sharesCount,
                onTap: _sharePost,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _actionButton({required IconData icon, required bool active, required int count, required VoidCallback onTap, Color? activeColor}) {
    return Column(
      children: [
        InkWell(
          onTap: onTap,
          child: Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.black45,
              borderRadius: BorderRadius.circular(30),
            ),
            child: Icon(icon, color: active ? (activeColor ?? Colors.white) : Colors.white, size: 28),
          ),
        ),
        const SizedBox(height: 4),
        Text(
          '$count',
          style: const TextStyle(color: Colors.white, fontSize: 12),
        ),
      ],
    );
  }

  Future<void> _react(ReactionType type) async {
    // Optimistic UI update
    setState(() {
      if (type == ReactionType.like) {
        if (_post.currentUserReaction == ReactionType.like) {
          _post = _post.copyWith(
            currentUserReaction: ReactionType.none,
            likesCount: _post.likesCount - 1,
          );
        } else {
          // لا يوجد تفاعل سلبى الآن
          _post = _post.copyWith(
            currentUserReaction: ReactionType.like,
            likesCount: _post.likesCount + 1,
          );
        }
      }
    });

    await SupabaseService().toggleReaction(postId: _post.id, reaction: type);
  }

  void _openComments() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (_) => CommentsSheet(
        post: _post,
        onCommentAdded: () async {
          // تحديث عدد التعليقات فوراً في الواجهة
          setState(() => _post = _post.copyWith(commentsCount: _post.commentsCount + 1));

          // إعادة تحميل عدد التعليقات الصحيح من قاعدة البيانات
          try {
            final actualCount = await SupabaseService().getPostCommentsCount(_post.id);
            if (mounted && actualCount != _post.commentsCount) {
              setState(() {
                _post = _post.copyWith(commentsCount: actualCount);
              });
            }
          } catch (e) {
            // في حالة الخطأ، نحتفظ بالعدد الحالي
          }
        },
      ),
    );
  }

  void _sharePost() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (_) => SharePostSheet(post: _post, onShared: () {
        setState(() => _post = _post.copyWith(sharesCount: _post.sharesCount + 1));
      }),
    );
  }

  // بناء زر التفاعل للجانب الأيسر
  Widget _buildReactionButton(ReactionType reactionType, IconData icon) {
    final isReacted = _post.currentUserReaction == reactionType;
    return GestureDetector(
      onTap: () => _react(reactionType),
      child: Container(
        width: 50,
        height: 50,
        decoration: BoxDecoration(
          color: isReacted ? Colors.blue.withValues(alpha: 0.8) : Colors.black.withValues(alpha: 0.6),
          shape: BoxShape.circle,
          border: Border.all(color: Colors.white.withValues(alpha: 0.3), width: 1),
        ),
        child: Icon(
          icon,
          color: isReacted ? Colors.white : Colors.white.withValues(alpha: 0.8),
          size: 24,
        ),
      ),
    );
  }

  // بناء أزرار الأسفل
  Widget _buildBottomButton(IconData icon, String text, VoidCallback? onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.black.withValues(alpha: 0.6),
              shape: BoxShape.circle,
            ),
            child: Icon(icon, color: Colors.white, size: 24),
          ),
          const SizedBox(height: 4),
          Text(text, style: const TextStyle(color: Colors.white, fontSize: 12)),
        ],
      ),
    );
  }

  // فتح المشاركة
  void _openShare() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (_) => SharePostSheet(post: _post, onShared: () {}),
    );
  }

  // بناء زر اللايك مع نفس طريقة المنشورات
  Widget _buildLikeButton() {
    return GestureDetector(
      onTap: () {
        if (_post.currentUserReaction == ReactionType.none || _post.currentUserReaction == ReactionType.like) {
          _react(ReactionType.like);
        } else {
          _react(ReactionType.like);
        }
      },
      onLongPressStart: (details) async {
        final selected = await _openReactionPickerAt(details.globalPosition);
        if (selected != null) {
          _react(selected);
        }
      },
      child: _buildActionButton(
        icon: Icons.thumb_up_alt_rounded,
        count: _post.likesCount,
        isActive: _post.currentUserReaction == ReactionType.like,
        activeColor: Colors.blue,
        onTap: null, // نستخدم onTap في GestureDetector
      ),
    );
  }

  // نفس دالة إظهار التفاعلات من post_card.dart
  Future<ReactionType?> _openReactionPickerAt(Offset globalPosition) async {
    final items = [
      ReactionType.like,
      ReactionType.dislike,
      ReactionType.love,
      ReactionType.funny,
      ReactionType.support,
      ReactionType.angry,
      ReactionType.sad,
    ];

    final screenSize = MediaQuery.of(context).size;
    final left = globalPosition.dx - 150; // توسيط التفاعلات
    final top = globalPosition.dy - 60;

    final selected = await showDialog<ReactionType>(
      context: context,
      barrierColor: Colors.transparent,
      builder: (ctx) {
        return Stack(children: [
          Positioned(
            left: left,
            top: top,
            child: _AnimatedReactionPicker(
              reactions: items,
              onReactionSelected: (reaction) => Navigator.pop(ctx, reaction),
            ),
          ),
        ]);
      },
    );

    return selected;
  }



  // بناء أزرار الإجراءات مثل TikTok
  Widget _buildActionButton({
    required IconData icon,
    required int count,
    bool isActive = false,
    Color? activeColor,
    VoidCallback? onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 50,
        height: 70,
        decoration: BoxDecoration(
          color: Colors.black.withValues(alpha: 0.3),
          borderRadius: BorderRadius.circular(25),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              color: isActive ? (activeColor ?? Colors.white) : Colors.white,
              size: 28,
            ),
            const SizedBox(height: 4),
            Text(
              count > 999 ? '${(count / 1000).toStringAsFixed(1)}K' : '$count',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

// نفس كلاس التفاعلات المتحركة من post_card.dart
class _AnimatedReactionPicker extends StatefulWidget {
  final List<ReactionType> reactions;
  final Function(ReactionType) onReactionSelected;

  const _AnimatedReactionPicker({
    required this.reactions,
    required this.onReactionSelected,
  });

  @override
  State<_AnimatedReactionPicker> createState() => _AnimatedReactionPickerState();
}

class _AnimatedReactionPickerState extends State<_AnimatedReactionPicker>
    with TickerProviderStateMixin {
  late List<AnimationController> _controllers;
  late List<Animation<double>> _scaleAnimations;
  late List<Animation<Offset>> _slideAnimations;
  late AnimationController _containerController;
  late Animation<double> _containerScaleAnimation;

  @override
  void initState() {
    super.initState();

    // كونترولر للحاوية الرئيسية
    _containerController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );

    _containerScaleAnimation = Tween<double>(begin: 0.8, end: 1.0).animate(
      CurvedAnimation(parent: _containerController, curve: Curves.easeOutBack),
    );

    // كونترولرز للتفاعلات الفردية - أسرع بكثير
    _controllers = List.generate(
      widget.reactions.length,
      (index) => AnimationController(
        duration: const Duration(milliseconds: 120), // أسرع جداً مثل Facebook
        vsync: this,
      ),
    );

    // أنيميشن التكبير والتصغير
    _scaleAnimations = _controllers.map((controller) {
      return Tween<double>(begin: 0.0, end: 1.0).animate(
        CurvedAnimation(parent: controller, curve: Curves.elasticOut),
      );
    }).toList();

    // أنيميشن الانزلاق من الأسفل
    _slideAnimations = _controllers.map((controller) {
      return Tween<Offset>(begin: const Offset(0, 1), end: Offset.zero).animate(
        CurvedAnimation(parent: controller, curve: Curves.easeOutBack),
      );
    }).toList();

    // بدء الأنيميشن
    _startAnimations();
  }

  void _startAnimations() async {
    // بدء أنيميشن الحاوية
    _containerController.forward();

    // بدء أنيميشن التفاعلات واحدة تلو الأخرى بسرعة
    for (int i = 0; i < _controllers.length; i++) {
      _controllers[i].forward();
      await Future.delayed(const Duration(milliseconds: 30)); // تأخير قصير جداً
    }
  }

  @override
  void dispose() {
    _containerController.dispose();
    for (var controller in _controllers) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _containerController,
      builder: (context, child) {
        return Transform.scale(
          scale: _containerScaleAnimation.value,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(25),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.2),
                  blurRadius: 8,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: widget.reactions.asMap().entries.map((entry) {
                final index = entry.key;
                final reaction = entry.value;

                return AnimatedBuilder(
                  animation: _controllers[index],
                  builder: (context, child) {
                    return Transform.scale(
                      scale: _scaleAnimations[index].value,
                      child: SlideTransition(
                        position: _slideAnimations[index],
                        child: _buildReaction(reaction),
                      ),
                    );
                  },
                );
              }).toList(),
            ),
          ),
        );
      },
    );
  }

  Widget _buildReaction(ReactionType reaction) {
    return GestureDetector(
      onTap: () => widget.onReactionSelected(reaction),
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 2),
        child: Container(
          padding: const EdgeInsets.all(4),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(20),
            gradient: LinearGradient(
              colors: [
                Colors.white.withValues(alpha: 0.9),
                Colors.white.withValues(alpha: 0.6),
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Image.asset(
            reaction.assetPath,
            width: 28,
            height: 28,
            fit: BoxFit.contain,
          ),
        ),
      ),
    );
  }
}